#!/bin/bash

# Simple script to collect all Image Builder AMI IDs from all regions

export AWS_ACCESS_KEY_ID="********************"
export AWS_SECRET_ACCESS_KEY="zwr+754nIzzRjQGs8o+yijzjK6KOZacpJVRt29VN"
export AWS_SESSION_TOKEN="IQoJb3JpZ2luX2VjEID//////////wEaCWV1LXdlc3QtMSJHMEUCIQCjjq62pE90ibENrtq0941t8hf9ihjhahMY2N7Nuy5slgIgMvIBXz+9KrCpr7BSsth9wkCWU87JhIXyOQulvPcPFMoqgwMIeRADGgw1NjEwOTc2Mjg1NzUiDDh0emxX4Z40GvZevyrgAmKKauPhmMZEktV35bUxfDbMjoR15bUp25lLbs1KpeM7bj13i/7uUoP/+a8+BoUhVzw1YJKD08CeZIAFBbzVwDjctYQLASJq2STUrTFrTnnp9y2n53B6E9gzpjMxkZWSeTYcDzmK4FKgA+oa+TOdsvFb1L1gEiwMr1BO5GYQ0Fd1qUe7tD1RTGBNiE2DizgbNC35m1ZdIdV4+3ZJCTI8PJrhJI59zPSiZGJ56+g5ssHyNvRJ0DWYyP2iCzFsKfC1hoANNaff7KkbyexI0oBHMX9Rxst0y4kjDw51GJ8iIfkVc0PpebtJfD2AAtAawTOAasyd1br4OMjUUsRCo4Yu7VAVAuUOkMoVUGO/rmx4Ede19I5Gx6Yin1LtyG5z+UF4/cqTr8VRwD15VHb8ZcaljqGVG/exzhpsrKuFHhSx78M4NeRDAzwCC4H/0SiJmVXiCu9OEGitCM/CLlb0stnkdbIwi/76wgY6pgFlyOIHUMFGWJrcMva/UH6IzJtfx54O+gUwqXsnP7IZfTzsx59K79dJ8sOpDTemFbCPiKH2t85ZGMfIoPeJ4ahPWW49zUZDghEH1oSnaaeFAziv6WN8fKowoXm/n9yF6IVdu/RtMH4FgkPYOq2Ab35JcGs0GeHySD10VYV6a8C4Rs7l2sQS/eqFhGGwswXdzGcVKs0EvUwrm4uY1/P25OG8EqNd4Nkw"
export AWS_REGION=eu-west-1

OUTPUT_FILE="imagebuilder-amis.txt"

# Get all AWS regions
REGIONS=$(aws ec2 describe-regions --query 'Regions[].RegionName' --output text)

# Clear output file
echo "" > $OUTPUT_FILE

# Process each region
for REGION in $REGIONS; do
    echo "Processing $REGION..."
    
    # Initialize next token for pagination
    NEXT_TOKEN=""
    
    # Loop through all pages of images
    while true; do
        # Build command with or without next-token
        if [ -z "$NEXT_TOKEN" ]; then
            RESPONSE=$(aws imagebuilder list-images --region $REGION --output json)
        else
            RESPONSE=$(aws imagebuilder list-images --region $REGION --next-token "$NEXT_TOKEN" --output json)
        fi
        
        # Extract image ARNs from current page
        echo "$RESPONSE" | jq -r '.imageVersionList[].arn' | \
        while read IMAGE_ARN; do
            if [ ! -z "$IMAGE_ARN" ]; then
                # Get image details and extract AMI IDs
                aws imagebuilder get-image --image-build-version-arn "$IMAGE_ARN" --region $REGION --output json | \
                jq -r --arg region "$REGION" '.image.outputResources.amis[]? | select(.region == $region) | .image' >> $OUTPUT_FILE
            fi
        done
        
        # Check if there's a next token
        NEXT_TOKEN=$(echo "$RESPONSE" | jq -r '.nextToken // empty')
        
        # Break if no more pages
        if [ -z "$NEXT_TOKEN" ]; then
            break
        fi
    done
done

# Remove duplicates and empty lines
sort $OUTPUT_FILE | uniq | grep -v '^$' > ${OUTPUT_FILE}.tmp
mv ${OUTPUT_FILE}.tmp $OUTPUT_FILE

echo "Done. AMI IDs saved to $OUTPUT_FILE"
echo "Total AMIs found: $(wc -l < $OUTPUT_FILE)"