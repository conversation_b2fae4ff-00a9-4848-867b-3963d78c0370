#!/bin/bash
# Script to collect instances across all accounts in organization

aws sso login --profile nxp-master

# Get list of all accounts
aws organizations list-accounts --query 'Accounts[?Status==`ACTIVE`].[Id,Name]' --output text > accounts.txt

# Create results file
echo "AccountId,AccountName,Region,InstanceId,AMI,InstanceType,State,Tags" > instances_report.csv

while read account_id account_name; do
    echo "Processing account: $account_name ($account_id)"
    
    # Assume cross-account role
    ROLE_ARN="arn:aws:iam::${account_id}:role/OrganizationAccountAccessRole"
    
    for region in us-east-1 us-west-2 eu-west-1; do  # Add your regions
        aws sts assume-role --role-arn $ROLE_ARN --role-session-name AMIAudit \
        --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]' \
        --output text | while read access_key secret_key session_token; do
            
            AWS_ACCESS_KEY_ID=$access_key \
            AWS_SECRET_ACCESS_KEY=$secret_key \
            AWS_SESSION_TOKEN=$session_token \
            aws ec2 describe-instances --region $region \
            --query 'Reservations[].Instances[].[InstanceId,ImageId,InstanceType,State.Name,Tags[?Key==`Name`].Value|[0]]' \
            --output text | while read instance_id ami instance_type state name; do
                echo "$account_id,$account_name,$region,$instance_id,$ami,$instance_type,$state,$name" >> instances_report.csv
            done
        done
    done
done < accounts.txt