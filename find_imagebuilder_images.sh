#!/bin/bash
# Script to find all images created by EC2 Image Builder across AWS organization accounts
# This script uses AWS CLI to query Image Builder service directly

set -e

# Configuration
export AWS_PROFILE=nxpshared
REGIONS=("us-east-1" "us-west-2" "eu-west-1")  # Add your regions
ACCOUNTS_FILE="accounts.txt"
OUTPUT_DIR="imagebuilder_results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Create output directory
mkdir -p "$OUTPUT_DIR"

# Output files
IB_IMAGES_FILE="$OUTPUT_DIR/imagebuilder_images_${TIMESTAMP}.json"
IB_AMIS_FILE="$OUTPUT_DIR/imagebuilder_amis_${TIMESTAMP}.json"
REPORT_FILE="$OUTPUT_DIR/imagebuilder_report_${TIMESTAMP}.csv"

echo "=== EC2 Image Builder Images Discovery ==="
echo "Timestamp: $(date)"
echo "Output directory: $OUTPUT_DIR"
echo ""

# Initialize output files
echo "[]" > "$IB_IMAGES_FILE"
echo "[]" > "$IB_AMIS_FILE"
echo "AccountId,AccountName,Region,Type,ResourceId,Name,CreationDate,Platform,Details" > "$REPORT_FILE"

# Function to assume role and get credentials
assume_role() {
    local account_id=$1
    local role_name=${2:-"OrganizationAccountAccessRole"}
    local role_arn="arn:aws:iam::${account_id}:role/${role_name}"
    
    aws sts assume-role \
        --role-arn "$role_arn" \
        --role-session-name "ImageBuilderAudit-${account_id}" \
        --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]' \
        --output text 2>/dev/null
}

# Function to get Image Builder images
get_imagebuilder_images() {
    local account_id=$1
    local account_name=$2
    local region=$3
    local access_key=$4
    local secret_key=$5
    local session_token=$6
    
    echo "    Querying Image Builder images in $region..."
    
    # Set temporary credentials
    export AWS_ACCESS_KEY_ID="$access_key"
    export AWS_SECRET_ACCESS_KEY="$secret_key"
    export AWS_SESSION_TOKEN="$session_token"
    
    # Get Image Builder images
    local images_json=$(aws imagebuilder list-images \
        --region "$region" \
        --output json 2>/dev/null || echo '{"imageVersionList":[]}')
    
    # Process each image
    echo "$images_json" | jq -r '.imageVersionList[]? | @base64' | while read -r image_b64; do
        if [ -n "$image_b64" ]; then
            local image=$(echo "$image_b64" | base64 --decode)
            local image_arn=$(echo "$image" | jq -r '.arn')
            local image_name=$(echo "$image" | jq -r '.name')
            local image_version=$(echo "$image" | jq -r '.version')
            local platform=$(echo "$image" | jq -r '.platform')
            local date_created=$(echo "$image" | jq -r '.dateCreated')
            
            # Get detailed image information to find AMI IDs
            local image_detail=$(aws imagebuilder get-image \
                --image-build-version-arn "$image_arn" \
                --region "$region" \
                --output json 2>/dev/null || echo '{}')
            
            local ami_ids=$(echo "$image_detail" | jq -r '.image.outputResources.amis[]?.image // empty' | tr '\n' ',' | sed 's/,$//')
            
            # Add to CSV report
            echo "$account_id,$account_name,$region,ImageBuilder Image,$image_arn,$image_name,$date_created,$platform,AMIs: $ami_ids" >> "$REPORT_FILE"
            
            # Add to JSON file (append to array)
            local image_record=$(echo "$image" | jq --arg account_id "$account_id" --arg account_name "$account_name" --arg region "$region" --arg ami_ids "$ami_ids" '. + {account_id: $account_id, account_name: $account_name, region: $region, ami_ids: $ami_ids}')
            
            # Append to images file
            local current_images=$(cat "$IB_IMAGES_FILE")
            echo "$current_images" | jq --argjson new_image "$image_record" '. += [$new_image]' > "$IB_IMAGES_FILE.tmp"
            mv "$IB_IMAGES_FILE.tmp" "$IB_IMAGES_FILE"
        fi
    done
}

# Function to get AMIs with Image Builder tags
get_imagebuilder_amis() {
    local account_id=$1
    local account_name=$2
    local region=$3
    local access_key=$4
    local secret_key=$5
    local session_token=$6
    
    echo "    Querying AMIs with Image Builder tags in $region..."
    
    # Set temporary credentials
    export AWS_ACCESS_KEY_ID="$access_key"
    export AWS_SECRET_ACCESS_KEY="$secret_key"
    export AWS_SESSION_TOKEN="$session_token"
    
    # Get AMIs owned by this account with potential Image Builder tags
    local amis_json=$(aws ec2 describe-images \
        --owners self \
        --region "$region" \
        --query 'Images[?Tags]' \
        --output json 2>/dev/null || echo '[]')
    
    # Process each AMI to check for Image Builder indicators
    echo "$amis_json" | jq -r '.[]? | @base64' | while read -r ami_b64; do
        if [ -n "$ami_b64" ]; then
            local ami=$(echo "$ami_b64" | base64 --decode)
            local ami_id=$(echo "$ami" | jq -r '.ImageId')
            local ami_name=$(echo "$ami" | jq -r '.Name // ""')
            local creation_date=$(echo "$ami" | jq -r '.CreationDate')
            local platform=$(echo "$ami" | jq -r '.Platform // "linux"')
            
            # Check tags for Image Builder indicators
            local imagebuilder_tags=$(echo "$ami" | jq -r '.Tags[]? | select(.Key | test("(?i)imagebuilder|createdby")) | "\(.Key)=\(.Value)"' | tr '\n' ';')
            local imagebuilder_values=$(echo "$ami" | jq -r '.Tags[]? | select(.Value | test("(?i)imagebuilder|image builder")) | "\(.Key)=\(.Value)"' | tr '\n' ';')
            
            local all_indicators="${imagebuilder_tags}${imagebuilder_values}"
            
            if [ -n "$all_indicators" ]; then
                # Add to CSV report
                echo "$account_id,$account_name,$region,AMI (Tagged),$ami_id,$ami_name,$creation_date,$platform,Tags: $all_indicators" >> "$REPORT_FILE"
                
                # Add to JSON file
                local ami_record=$(echo "$ami" | jq --arg account_id "$account_id" --arg account_name "$account_name" --arg region "$region" --arg indicators "$all_indicators" '. + {account_id: $account_id, account_name: $account_name, region: $region, imagebuilder_indicators: $indicators}')
                
                # Append to AMIs file
                local current_amis=$(cat "$IB_AMIS_FILE")
                echo "$current_amis" | jq --argjson new_ami "$ami_record" '. += [$new_ami]' > "$IB_AMIS_FILE.tmp"
                mv "$IB_AMIS_FILE.tmp" "$IB_AMIS_FILE"
            fi
        fi
    done
}

# Function to process a single account
process_account() {
    local account_id=$1
    local account_name=$2
    
    echo "Processing account: $account_name ($account_id)"
    
    # Assume cross-account role
    local credentials
    credentials=$(assume_role "$account_id")
    
    if [ $? -ne 0 ] || [ -z "$credentials" ]; then
        echo "  Error: Could not assume role for account $account_id"
        return 1
    fi
    
    # Parse credentials
    local access_key secret_key session_token
    read -r access_key secret_key session_token <<< "$credentials"
    
    if [ -z "$access_key" ] || [ -z "$secret_key" ] || [ -z "$session_token" ]; then
        echo "  Error: Invalid credentials for account $account_id"
        return 1
    fi
    
    # Process each region
    for region in "${REGIONS[@]}"; do
        echo "  Checking region: $region"
        
        # Get Image Builder images
        get_imagebuilder_images "$account_id" "$account_name" "$region" "$access_key" "$secret_key" "$session_token"
        
        # Get AMIs with Image Builder tags
        get_imagebuilder_amis "$account_id" "$account_name" "$region" "$access_key" "$secret_key" "$session_token"
    done
    
    # Reset credentials
    unset AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN
}

# Main execution
echo "Reading accounts from: $ACCOUNTS_FILE"

if [ ! -f "$ACCOUNTS_FILE" ]; then
    echo "Error: $ACCOUNTS_FILE not found"
    exit 1
fi

# Process each account
while IFS=$'\t' read -r account_id account_name; do
    # Skip empty lines and comments
    if [[ -z "$account_id" || "$account_id" =~ ^#.* ]]; then
        continue
    fi
    
    process_account "$account_id" "$account_name"
    echo ""
done < "$ACCOUNTS_FILE"

# Generate summary
echo "=== SUMMARY ==="
echo "Image Builder Images found: $(jq length "$IB_IMAGES_FILE")"
echo "AMIs with Image Builder tags: $(jq length "$IB_AMIS_FILE")"
echo ""
echo "Output files:"
echo "  - Image Builder Images: $IB_IMAGES_FILE"
echo "  - Tagged AMIs: $IB_AMIS_FILE"
echo "  - CSV Report: $REPORT_FILE"
echo ""
echo "To view the CSV report:"
echo "  cat $REPORT_FILE | column -t -s ','"
echo ""
echo "To get unique AMI IDs from Image Builder:"
echo "  jq -r '.[].ami_ids' $IB_IMAGES_FILE | tr ',' '\n' | sort -u"
